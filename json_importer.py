from flask import Blueprint, render_template, request, jsonify, flash
import json
import os
from datetime import datetime
from database_manager import db, Channel

json_importer = Blueprint('json_importer', __name__)

def load_json_data():
    with open('data/channels.json', 'r', encoding='utf-8') as f:
        channels = json.load(f)
    with open('data/languages.json', 'r', encoding='utf-8') as f:
        languages = json.load(f)
    with open('data/countries.json', 'r', encoding='utf-8') as f:
        countries = json.load(f)
    return channels, languages, countries

@json_importer.route('/json/browse')
def browse_json():
    # This route will now primarily render the HTML template
    # The initial channel data will be loaded via an AJAX call from the frontend
    channels, _, countries = load_json_data() # Still load to pass countries for filtering if needed
    return render_template('json_importer/browse.html',
                         countries=countries)

@json_importer.route('/json/channels_paginated')
def channels_paginated():
    offset = request.args.get('offset', 0, type=int)
    limit = request.args.get('limit', 100, type=int)
    search_query = request.args.get('search', '').lower()

    all_channels, _, _ = load_json_data()

    # Filter channels based on search query
    filtered_channels = []
    if search_query:
        for channel in all_channels:
            if search_query in channel['name'].lower() or \
               any(search_query in alt.lower() for alt in channel.get('alt_names', [])):
                filtered_channels.append(channel)
    else:
        filtered_channels = all_channels

    total_channels = len(filtered_channels)
    paginated_channels = filtered_channels[offset:offset + limit]

    return jsonify({
        'channels': paginated_channels,
        'total': total_channels,
        'offset': offset,
        'limit': limit
    })

@json_importer.route('/json/import', methods=['POST'])
def import_channel():
    channel_id = request.form.get('channel_id')
    channels, _, _ = load_json_data()

    channel_data = next((c for c in channels if c['id'] == channel_id), None)
    if not channel_data:
        return jsonify({'success': False, 'message': 'Channel not found'})

    # Check if channel already exists
    if Channel.query.get(channel_id):
        return jsonify({'success': False, 'message': 'Channel already exists in database'})

    try:
        channel = Channel(
            id=channel_data['id'],
            name=channel_data['name'],
            alt_names=','.join(channel_data['alt_names']),
            network=channel_data['network'],
            owners=','.join(channel_data['owners']),
            country=channel_data['country'],
            subdivision=channel_data['subdivision'],
            city=channel_data['city'],
            categories=','.join(channel_data['categories']),
            is_nsfw=channel_data['is_nsfw'],
            launched=channel_data['launched'],
            closed=channel_data['closed'],
            replaced_by=channel_data['replaced_by'],
            website=channel_data['website'],
            logo=channel_data['logo']
        )
        db.session.add(channel)
        db.session.commit()
        return jsonify({'success': True, 'message': 'Channel imported successfully'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@json_importer.route('/json/batch_import', methods=['POST'])
def batch_import():
    selected_ids = request.form.getlist('channel_ids')
    channels, _, _ = load_json_data()

    imported = 0
    skipped = 0
    errors = []

    # Fetch all existing channel IDs in a single query for efficient lookup
    existing_channel_ids = {c.id for c in Channel.query.with_entities(Channel.id).all()}

    for channel_id in selected_ids:
        channel_data = next((c for c in channels if c['id'] == channel_id), None)
        if not channel_data:
            errors.append(f"Channel {channel_id} not found in JSON")
            continue

        # Check if channel already exists using the set
        if channel_id in existing_channel_ids:
            skipped += 1
            continue

        try:
            channel = Channel(
                id=channel_data['id'],
                name=channel_data['name'],
                alt_names=','.join(channel_data['alt_names']),
                network=channel_data['network'],
                owners=','.join(channel_data['owners']),
                country=channel_data['country'],
                subdivision=channel_data['subdivision'],
                city=channel_data['city'],
                categories=','.join(channel_data['categories']),
                is_nsfw=channel_data['is_nsfw'],
                launched=channel_data['launched'],
                closed=channel_data['closed'],
                replaced_by=channel_data['replaced_by'],
                website=channel_data['website'],
                logo=channel_data['logo']
            )
            db.session.add(channel)
            imported += 1
        except Exception as e:
            errors.append(str(e))

    db.session.commit()

    return jsonify({
        'success': True,
        'imported': imported,
        'skipped': skipped,
        'errors': errors
    })

def clean_channel_name_for_json_matching(name):
    """
    Cleans channel name for matching using the same logic as in database_manager.py
    """
    import re
    if not name:
        return ""

    # Convert to uppercase for consistent processing
    cleaned_name = name.upper()

    # Remove quality indicators (case-insensitive, with word boundaries)
    quality_keywords = [
        'HD', 'FHD', 'SD', '4K', 'HVEC', 'TNT', 'UHD', '8K',
        'HEVC', 'H264', 'H265', 'H.264', 'H.265', 'FULL HD',
        'ULTRA HD', 'HIGH DEF', 'STANDARD DEF'
    ]

    for keyword in quality_keywords:
        # Remove keyword with word boundaries, handling various separators
        patterns = [
            rf'\b{re.escape(keyword)}\b',  # Standard word boundary
            rf'[\s\-_\.]*{re.escape(keyword)}[\s\-_\.]*',  # With separators
            rf'^{re.escape(keyword)}[\s\-_\.]+',  # At beginning with separators
            rf'[\s\-_\.]+{re.escape(keyword)}$'   # At end with separators
        ]
        for pattern in patterns:
            cleaned_name = re.sub(pattern, ' ', cleaned_name, flags=re.IGNORECASE)

    # Remove common separators and normalize spacing
    cleaned_name = re.sub(r'[\-_\.]+', ' ', cleaned_name)  # Replace separators with spaces
    cleaned_name = re.sub(r'\s+', ' ', cleaned_name)  # Normalize multiple spaces to single space
    cleaned_name = cleaned_name.strip()  # Remove leading/trailing spaces

    # Remove common prefixes/suffixes that might interfere with matching
    prefixes_suffixes = ['TV', 'CHANNEL', 'LIVE', 'STREAM']
    for term in prefixes_suffixes:
        # Remove if it's a standalone word at beginning or end
        cleaned_name = re.sub(rf'^\b{re.escape(term)}\b\s*', '', cleaned_name, flags=re.IGNORECASE)
        cleaned_name = re.sub(rf'\s*\b{re.escape(term)}\b$', '', cleaned_name, flags=re.IGNORECASE)

    return cleaned_name.strip()

@json_importer.route('/json/search_channels_by_name')
def search_json_channels_by_name(query): # Modified to accept query as argument
    channels, _, _ = load_json_data()

    # Clean the query for better matching
    cleaned_query = clean_channel_name_for_json_matching(query)

    matches = []
    for channel in channels:
        # Clean channel name and alt names for comparison
        cleaned_channel_name = clean_channel_name_for_json_matching(channel['name'])

        # Check exact match first
        if cleaned_query == cleaned_channel_name:
            matches.append({
                'id': channel['id'],
                'name': channel['name'],
                'alt_names': ', '.join(channel.get('alt_names', []))
            })
            continue

        # Check alt names for exact match
        found_in_alt = False
        for alt_name in channel.get('alt_names', []):
            cleaned_alt_name = clean_channel_name_for_json_matching(alt_name)
            if cleaned_query == cleaned_alt_name:
                matches.append({
                    'id': channel['id'],
                    'name': channel['name'],
                    'alt_names': ', '.join(channel.get('alt_names', []))
                })
                found_in_alt = True
                break

        if found_in_alt:
            continue

        # Check partial matches as fallback (only for reasonable length queries)
        if len(cleaned_query) >= 3:
            # Only match if the query is contained in the channel name (not vice versa)
            # and the match is substantial (at least 50% of the shorter string)
            if cleaned_query in cleaned_channel_name:
                shorter_len = min(len(cleaned_query), len(cleaned_channel_name))
                longer_len = max(len(cleaned_query), len(cleaned_channel_name))
                if shorter_len / longer_len >= 0.5:  # At least 50% match
                    matches.append({
                        'id': channel['id'],
                        'name': channel['name'],
                        'alt_names': ', '.join(channel.get('alt_names', []))
                    })
                    continue

            # Check partial matches in alt names
            for alt_name in channel.get('alt_names', []):
                cleaned_alt_name = clean_channel_name_for_json_matching(alt_name)
                if cleaned_query in cleaned_alt_name:
                    shorter_len = min(len(cleaned_query), len(cleaned_alt_name))
                    longer_len = max(len(cleaned_query), len(cleaned_alt_name))
                    if shorter_len / longer_len >= 0.5:  # At least 50% match
                        matches.append({
                            'id': channel['id'],
                            'name': channel['name'],
                            'alt_names': ', '.join(channel.get('alt_names', []))
                        })
                        break

    return matches # Return list of matches, not jsonify

# New route for external calls that need to search JSON
@json_importer.route('/json/search_channels_by_name_route')
def search_json_channels_by_name_route():
    query = request.args.get('query', '')
    matches = search_json_channels_by_name(query)
    return jsonify(matches)
