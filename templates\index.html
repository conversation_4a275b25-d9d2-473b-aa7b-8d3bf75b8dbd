{% extends "base.html" %} {% block content %}
<div class="container">
  <div class="row justify-content-center mt-5">
    <div class="col-md-8 text-center">
      <h1 class="display-4 mb-4">TV Channel Manager</h1>
      <p class="lead mb-5">
        A comprehensive tool to manage and import TV channel data
      </p>

      <div class="row">
        <div class="col-md-4 mb-4">
          <div class="card h-100">
            <div class="card-body">
              <h3 class="card-title">
                <i class="bi bi-database"></i> Database Manager
              </h3>
              <p class="card-text">
                View, edit, and manage channels already in your database. Add
                new channels manually with full control over all fields.
              </p>
            </div>
            <div class="card-footer bg-transparent">
              <a href="/db" class="btn btn-primary btn-lg">
                Go to Database Manager <i class="bi bi-arrow-right"></i>
              </a>
            </div>
          </div>
        </div>

        <div class="col-md-4 mb-4">
          <div class="card h-100">
            <div class="card-body">
              <h3 class="card-title">
                <i class="bi bi-file-earmark-arrow-down"></i> JSON Importer
              </h3>
              <p class="card-text">
                Quickly import channels from JSON files to your database. Bulk
                import or add individual channels with one click.
              </p>
            </div>
            <div class="card-footer bg-transparent">
              <a href="/json/browse" class="btn btn-success btn-lg">
                Go to JSON Importer <i class="bi bi-arrow-right"></i>
              </a>
            </div>
          </div>
        </div>

        <div class="col-md-4 mb-4">
          <div class="card h-100">
            <div class="card-body">
              <h3 class="card-title">
                <i class="bi bi-broadcast"></i> TV Channel Importer
              </h3>
              <p class="card-text">
                Import TV channels from M3U/IPTV lists. Automatically match
                channels with your database and add streaming sources.
              </p>
            </div>
            <div class="card-footer bg-transparent">
              <a href="/tv_channel_importer" class="btn btn-warning btn-lg">
                Go to TV Channel Importer <i class="bi bi-arrow-right"></i>
              </a>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-5">
        <h3>Recent Activity</h3>
        <div class="list-group">
          {% if recent_activity %} {% for activity in recent_activity %}
          <div class="list-group-item">
            <div class="d-flex w-100 justify-content-between">
              <h5 class="mb-1">
                {{ activity.action }}: {{ activity.channel_name }}
              </h5>
              <small>{{ activity.timestamp }}</small>
            </div>
            <p class="mb-1">ID: {{ activity.channel_id }}</p>
          </div>
          {% endfor %} {% else %}
          <div class="list-group-item">
            <p class="mb-1">No recent activity yet</p>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block styles %}
<style>
  .card {
    transition: transform 0.3s;
  }
  .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  .display-4 {
    font-weight: 300;
  }
  .lead {
    color: #6c757d;
  }
</style>
{% endblock %}
