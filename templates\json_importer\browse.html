{% extends "base.html" %} {% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
  <h2>Import Channels from JSON</h2>
  <button id="batchImportBtn" class="btn btn-success" disabled>
    <i class="bi bi-download"></i> Import Selected
  </button>
</div>

<div class="row mb-4">
  <div class="col-md-6">
    <input
      type="text"
      id="searchInput"
      class="form-control"
      placeholder="Search channels..."
    />
  </div>
  <div class="col-md-6">
    <select id="countryFilter" class="form-select">
      <option value="">All Countries</option>
      {% for country in countries %}
      <option value="{{ country.code }}">
        {{ country.name }} ({{ country.code }})
      </option>
      {% endfor %}
    </select>
  </div>
</div>

<form id="importForm" action="/json/batch_import" method="POST">
  <table class="table table-striped table-hover">
    <thead>
      <tr>
        <th><input type="checkbox" id="selectAll" /></th>
        <th>ID</th>
        <th>Name</th>
        <th>Country</th>
        <th>Categories</th>
        <th>Status</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody id="channelsTableBody">
      <!-- Channels will be loaded here by JavaScript -->
    </tbody>
  </table>
  <div id="loadingSpinner" class="text-center my-3" style="display: none">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
  <div id="endOfResults" class="text-center my-3" style="display: none">
    No more channels to load.
  </div>
</form>

{% endblock %} {% block scripts %}
<script>
  document.addEventListener('DOMContentLoaded', function () {
    const channelsTableBody = document.getElementById('channelsTableBody')
    const batchImportBtn = document.getElementById('batchImportBtn')
    const selectAllCheckbox = document.getElementById('selectAll')
    const searchInput = document.getElementById('searchInput')
    const countryFilterSelect = document.getElementById('countryFilter')
    const loadingSpinner = document.getElementById('loadingSpinner')
    const endOfResults = document.getElementById('endOfResults')

    let offset = 0
    const limit = 100
    let isLoading = false
    let allChannelsLoaded = false
    let existingChannelIds = new Set() // To store IDs of channels already in DB

    // Function to fetch existing channel IDs from the database
    async function fetchExistingChannelIds() {
      try {
        const response = await fetch('/get_existing_channel_ids') // Assuming this endpoint exists or will be created
        const data = await response.json()
        existingChannelIds = new Set(data.existing_ids)
      } catch (error) {
        console.error('Error fetching existing channel IDs:', error)
      }
    }

    // Function to render a single channel row
    function renderChannelRow(channel) {
      const exists = existingChannelIds.has(channel.id)
      const row = document.createElement('tr')
      row.setAttribute('data-country', channel.country)
      row.setAttribute('data-search', channel.name.toLowerCase())
      row.innerHTML = `
                <td>
                    <input type="checkbox" name="channel_ids" value="${
                      channel.id
                    }" ${exists ? 'disabled' : ''}/>
                </td>
                <td>${channel.id}</td>
                <td>${channel.name}</td>
                <td>${channel.country}</td>
                <td>${channel.categories.join(', ')}</td>
                <td>
                    ${
                      exists
                        ? '<span class="badge bg-secondary">Already in DB</span>'
                        : '<span class="badge bg-success">Available</span>'
                    }
                </td>
                <td>
                    ${
                      !exists
                        ? `<button type="button" class="btn btn-sm btn-primary import-btn" data-id="${channel.id}">
                                <i class="bi bi-download"></i> Import
                            </button>`
                        : ''
                    }
                </td>
            `
      channelsTableBody.appendChild(row)

      // Add event listener for single import button
      if (!exists) {
        row.querySelector('.import-btn').addEventListener('click', function () {
          const channelId = this.getAttribute('data-id')
          fetch('/json/import', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `channel_id=${channelId}`,
          })
            .then(response => response.json())
            .then(data => {
              if (data.success) {
                alert('Channel imported successfully!')
                // Update the status of the imported channel
                const importedCheckbox = row.querySelector(
                  `input[value="${channelId}"]`
                )
                if (importedCheckbox) {
                  importedCheckbox.disabled = true
                  importedCheckbox.checked = false
                  updateBatchImportBtn()
                }
                row.querySelector('td:nth-child(6)').innerHTML =
                  '<span class="badge bg-secondary">Already in DB</span>'
                row.querySelector('td:nth-child(7)').innerHTML = '' // Remove import button
              } else {
                alert('Error: ' + data.message)
              }
            })
        })
      }
    }

    // Function to load channels
    async function loadChannels() {
      if (isLoading || allChannelsLoaded) return
      isLoading = true
      loadingSpinner.style.display = 'block'

      const searchTerm = searchInput.value.toLowerCase()
      const countryFilter = countryFilterSelect.value

      try {
        const response = await fetch(
          `/json/channels_paginated?offset=${offset}&limit=${limit}&search=${encodeURIComponent(
            searchTerm
          )}&country=${encodeURIComponent(countryFilter)}`
        )
        const data = await response.json()

        if (data.channels.length === 0) {
          allChannelsLoaded = true
          endOfResults.style.display = 'block'
        } else {
          data.channels.forEach(renderChannelRow)
          offset += data.channels.length
          if (offset >= data.total) {
            allChannelsLoaded = true
            endOfResults.style.display = 'block'
          }
        }
      } catch (error) {
        console.error('Error loading channels:', error)
        alert('Failed to load channels. Please try again.')
      } finally {
        isLoading = false
        loadingSpinner.style.display = 'none'
        updateBatchImportBtn() // Update batch import button state after loading
      }
    }

    // Initial load
    fetchExistingChannelIds().then(() => {
      loadChannels()
    })

    // Infinite scroll
    window.addEventListener('scroll', () => {
      if (
        window.innerHeight + window.scrollY >=
          document.body.offsetHeight - 500 && // 500px from bottom
        !isLoading &&
        !allChannelsLoaded
      ) {
        loadChannels()
      }
    })

    // Batch import
    function updateBatchImportBtn() {
      const checked =
        document.querySelectorAll('input[name="channel_ids"]:checked').length >
        0
      batchImportBtn.disabled = !checked
    }

    selectAllCheckbox.addEventListener('change', function () {
      document
        .querySelectorAll('input[name="channel_ids"]:not(:disabled)')
        .forEach(checkbox => {
          checkbox.checked = this.checked
        })
      updateBatchImportBtn()
    })

    channelsTableBody.addEventListener('change', function (event) {
      if (event.target.matches('input[name="channel_ids"]')) {
        updateBatchImportBtn()
      }
    })

    batchImportBtn.addEventListener('click', function () {
      if (confirm('Are you sure you want to import all selected channels?')) {
        document.getElementById('importForm').submit()
      }
    })

    // Filtering (re-implemented to work with dynamic content)
    let debounceTimer
    searchInput.addEventListener('input', () => {
      clearTimeout(debounceTimer)
      debounceTimer = setTimeout(() => {
        resetAndLoadChannels()
      }, 300) // Debounce for 300ms
    })

    countryFilterSelect.addEventListener('change', () => {
      resetAndLoadChannels()
    })

    function resetAndLoadChannels() {
      channelsTableBody.innerHTML = '' // Clear existing channels
      offset = 0 // Reset offset
      allChannelsLoaded = false // Reset flag
      endOfResults.style.display = 'none'
      loadChannels() // Load channels with new filters
    }
  })
</script>
{% endblock %}
