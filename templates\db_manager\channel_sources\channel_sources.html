{% extends "base.html" %} {% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
  <h2>Manage Channel Sources</h2>
  <a href="/db/channel_sources/add" class="btn btn-primary">
    <i class="bi bi-plus-circle"></i> Add Channel Source
  </a>
</div>

<form method="GET" class="mb-4">
  <div class="input-group">
    <input
      type="text"
      class="form-control"
      placeholder="Search channel sources..."
      name="search_query"
      value="{{ search_query if search_query }}"
    />
    <button class="btn btn-outline-secondary" type="submit">Search</button>
  </div>
</form>

<div class="table-responsive">
  <table class="table table-striped table-hover">
    <thead>
      <tr>
        <th>Source ID</th>
        <th>Channel ID</th>
        <th>Source URL</th>
        <th>Provider ID</th>
        <th>Priority</th>
        <th>Active</th>
        <th>Last Checked</th>
        <th>Last Status</th>
        <th>Retry Count</th>
        <th>External</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      {% for source in channel_sources %}
      <tr>
        <td>{{ source.source_id }}</td>
        <td>{{ source.channel_id }}</td>
        <td class="truncate-text" title="{{ source.source_url }}">
          {{ source.source_url }}
        </td>
        <td>{{ source.provider_id }}</td>
        <td>{{ source.priority }}</td>
        <td>{{ source.is_active }}</td>
        <td>{{ source.last_checked }}</td>
        <td>{{ source.last_status }}</td>
        <td>{{ source.retry_count }}</td>
        <td>{{ source.is_external }}</td>
        <td>
          <a
            href="/db/channel_sources/edit/{{ source.source_id }}"
            class="btn btn-sm btn-warning"
          >
            <i class="bi bi-pencil"></i>
          </a>
          <a
            href="/db/channel_sources/delete/{{ source.source_id }}"
            class="btn btn-sm btn-danger"
            onclick="return confirm('Are you sure you want to delete this channel source?')"
          >
            <i class="bi bi-trash"></i>
          </a>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>

<div id="loading-spinner" class="text-center my-3" style="display: none">
  <div class="spinner-border text-primary" role="status">
    <span class="visually-hidden">Loading...</span>
  </div>
</div>

<script>
  let offset = parseInt('{{ offset + limit }}')
  const limit = parseInt('{{ limit }}')
  const search_query = "{{ search_query | default('') }}"
  let isLoading = false
  let allChannelSourcesLoaded = false

  function loadMoreChannelSources() {
    if (isLoading || allChannelSourcesLoaded) {
      return
    }
    isLoading = true
    document.getElementById('loading-spinner').style.display = 'block'

    const url = `/db/channel_sources/load_more?offset=${offset}&limit=${limit}&search_query=${search_query}`

    fetch(url)
      .then(response => response.json())
      .then(data => {
        if (data.length === 0) {
          allChannelSourcesLoaded = true
          console.log('All channel sources loaded.')
        } else {
          const tbody = document.querySelector('table tbody')
          data.forEach(source => {
            const row = `
              <tr>
                <td>${source.source_id}</td>
                <td>${source.channel_id}</td>
                <td class="truncate-text" title="${source.source_url}">${
              source.source_url
            }</td>
                <td>${source.provider_id || ''}</td>
                <td>${source.priority || ''}</td>
                <td>${source.is_active}</td>
                <td>${source.last_checked || ''}</td>
                <td>${source.last_status || ''}</td>
                <td>${source.retry_count || ''}</td>
                <td>${source.is_external}</td>
                <td>
                  <a href="/db/channel_sources/edit/${
                    source.source_id
                  }" class="btn btn-sm btn-warning">
                    <i class="bi bi-pencil"></i>
                  </a>
                  <a href="/db/channel_sources/delete/${
                    source.source_id
                  }" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this channel source?')">
                    <i class="bi bi-trash"></i>
                  </a>
                </td>
              </tr>
            `
            tbody.insertAdjacentHTML('beforeend', row)
          })
          offset += data.length
        }
      })
      .catch(error => {
        console.error('Error loading more channel sources:', error)
      })
      .finally(() => {
        isLoading = false
        document.getElementById('loading-spinner').style.display = 'none'
      })
  }

  window.addEventListener('scroll', () => {
    const { scrollTop, scrollHeight, clientHeight } = document.documentElement
    if (scrollTop + clientHeight >= scrollHeight - 500) {
      // 500px from bottom
      loadMoreChannelSources()
    }
  })

  // Initial load if page is not full
  document.addEventListener('DOMContentLoaded', () => {
    if (
      document.documentElement.scrollHeight <=
      document.documentElement.clientHeight
    ) {
      loadMoreChannelSources()
    }
  })
</script>
{% endblock %}
