{% extends "base.html" %} {% block content %}
<div class="p-4">
  <h2>{% if channel_source %}Edit{% else %}Add{% endif %} Channel Source</h2>

  <form method="POST">
    <div class="row mb-3">
      <div class="col-md-6">
        <label for="source_id" class="form-label">Source ID</label>
        <input
          type="text"
          class="form-control"
          id="source_id"
          name="source_id"
          value="{{ channel_source.source_id if channel_source }}"
          required
          {%
          if
          channel_source
          %}readonly{%
          endif
          %}
        />
      </div>
      <div class="col-md-6">
        <label for="channel_id" class="form-label">Channel ID</label>
        <input
          type="text"
          class="form-control"
          id="channel_id"
          name="channel_id"
          value="{{ channel_source.channel_id if channel_source }}"
          required
        />
      </div>
    </div>

    <div class="mb-3">
      <label for="source_url" class="form-label">Source URL</label>
      <input
        type="text"
        class="form-control"
        id="source_url"
        name="source_url"
        value="{{ channel_source.source_url if channel_source }}"
        required
      />
    </div>

    <div class="mb-3">
      <label for="provider_id" class="form-label">Provider ID</label>
      <input
        type="number"
        class="form-control"
        id="provider_id"
        name="provider_id"
        value="{{ channel_source.provider_id if channel_source }}"
      />
    </div>

    <div class="mb-3">
      <label for="priority" class="form-label">Priority</label>
      <input
        type="number"
        class="form-control"
        id="priority"
        name="priority"
        value="{{ channel_source.priority if channel_source }}"
      />
    </div>

    <div class="mb-3 form-check">
      <input
        type="checkbox"
        class="form-check-input"
        id="is_active"
        name="is_active"
        {%
        if
        channel_source
        and
        channel_source.is_active
        %}checked{%
        endif
        %}
      />
      <label class="form-check-label" for="is_active">Is Active</label>
    </div>

    <div class="mb-3">
      <label for="last_checked" class="form-label">Last Checked</label>
      <input
        type="text"
        class="form-control"
        id="last_checked"
        name="last_checked"
        value="{{ channel_source.last_checked if channel_source }}"
      />
    </div>

    <div class="mb-3">
      <label for="last_status" class="form-label">Last Status</label>
      <input
        type="number"
        class="form-control"
        id="last_status"
        name="last_status"
        value="{{ channel_source.last_status if channel_source }}"
      />
    </div>

    <div class="mb-3">
      <label for="retry_count" class="form-label">Retry Count</label>
      <input
        type="number"
        class="form-control"
        id="retry_count"
        name="retry_count"
        value="{{ channel_source.retry_count if channel_source }}"
      />
    </div>

    <div class="mb-3 form-check">
      <input
        type="checkbox"
        class="form-check-input"
        id="is_external"
        name="is_external"
        {%
        if
        channel_source
        and
        channel_source.is_external
        %}checked{%
        endif
        %}
      />
      <label class="form-check-label" for="is_external">Is External</label>
    </div>

    <button type="submit" class="btn btn-primary">
      {% if channel_source %}Update{% else %}Add{% endif %} Channel Source
    </button>
  </form>
</div>
