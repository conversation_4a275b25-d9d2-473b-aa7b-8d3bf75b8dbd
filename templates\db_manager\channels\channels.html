{% extends "base.html" %} {% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
  <h2>Manage Channels</h2>
  <a href="/db/channels/add" class="btn btn-primary">
    <i class="bi bi-plus-circle"></i> Add Channel
  </a>
</div>

<form method="GET" class="mb-4">
  <div class="input-group">
    <input
      type="text"
      class="form-control"
      placeholder="Search channels..."
      name="search_query"
      value="{{ search_query if search_query }}"
    />
    <button class="btn btn-outline-secondary" type="submit">Search</button>
  </div>
</form>

<div class="table-responsive">
  <table class="table table-striped table-hover">
    <thead>
      <tr>
        <th>Channel ID</th>
        <th>Name</th>
        <th>Alt Names</th>
        <th>Country Code</th>
        <th>Category ID</th>
        <th>Logo</th>
        <th>Active</th>
        <th>Created At</th>
        <th>Language Code</th>
        <th>Group ID</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      {% for channel in channels %}
      <tr>
        <td>{{ channel.channel_id }}</td>
        <td>{{ channel.name }}</td>
        <td>{{ channel.alt_names }}</td>
        <td>{{ channel.country_code }}</td>
        <td>{{ channel.category_id }}</td>
        <td>
          {% if channel.logo_url %}
          <img
            src="{{ channel.logo_url }}"
            alt="Logo"
            class="channel-logo"
            referrerpolicy="no-referrer"
            onerror="this.onerror=null;this.src='https://www.freeiconspng.com/thumbs/television-icon/tv-icon-13.png';"
          />
          {% else %} N/A {% endif %}
        </td>
        <td>{{ channel.is_active }}</td>
        <td>{{ channel.created_at }}</td>
        <td>{{ channel.language_code }}</td>
        <td>{{ channel.group_id }}</td>
        <td>
          <a
            href="/db/channels/edit/{{ channel.channel_id }}"
            class="btn btn-sm btn-warning"
          >
            <i class="bi bi-pencil"></i>
          </a>
          <a
            href="/db/channels/delete/{{ channel.channel_id }}"
            class="btn btn-sm btn-danger"
            onclick="return confirm('Are you sure you want to delete this channel?')"
          >
            <i class="bi bi-trash"></i>
          </a>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>

<div id="loading-spinner" class="text-center my-3" style="display: none">
  <div class="spinner-border text-primary" role="status">
    <span class="visually-hidden">Loading...</span>
  </div>
</div>

<script>
  let offset = parseInt('{{ offset + limit }}')
  const limit = parseInt('{{ limit }}')
  const search_query = "{{ search_query | default('') }}"
  let isLoading = false
  let allChannelsLoaded = false

  function loadMoreChannels() {
    if (isLoading || allChannelsLoaded) {
      return
    }
    isLoading = true
    document.getElementById('loading-spinner').style.display = 'block'

    const url = `/db/channels/load_more?offset=${offset}&limit=${limit}&search_query=${search_query}`

    fetch(url)
      .then(response => response.json())
      .then(data => {
        if (data.length === 0) {
          allChannelsLoaded = true
          console.log('All channels loaded.')
        } else {
          const tbody = document.querySelector('table tbody')
          data.forEach(channel => {
            const row = `
              <tr>
                <td>${channel.channel_id}</td>
                <td>${channel.name}</td>
                <td>${channel.alt_names || ''}</td>
                <td>${channel.country_code || ''}</td>
                <td>${channel.category_id || ''}</td>
                <td>
                  ${
                    channel.logo_url
                      ? `<img src="${channel.logo_url}" alt="Logo" class="channel-logo" referrerpolicy="no-referrer" onerror="this.onerror=null;this.src='https://via.placeholder.com/50x50?text=No+Logo';" />`
                      : 'N/A'
                  }
                </td>
                <td>${channel.is_active}</td>
                <td>${channel.created_at || ''}</td>
                <td>${channel.language_code || ''}</td>
                <td>${channel.group_id || ''}</td>
                <td>
                  <a href="/db/channels/edit/${
                    channel.channel_id
                  }" class="btn btn-sm btn-warning">
                    <i class="bi bi-pencil"></i>
                  </a>
                  <a href="/db/channels/delete/${
                    channel.channel_id
                  }" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this channel?')">
                    <i class="bi bi-trash"></i>
                  </a>
                </td>
              </tr>
            `
            tbody.insertAdjacentHTML('beforeend', row)
          })
          offset += data.length
        }
      })
      .catch(error => {
        console.error('Error loading more channels:', error)
      })
      .finally(() => {
        isLoading = false
        document.getElementById('loading-spinner').style.display = 'none'
      })
  }

  window.addEventListener('scroll', () => {
    const { scrollTop, scrollHeight, clientHeight } = document.documentElement
    if (scrollTop + clientHeight >= scrollHeight - 500) {
      // 500px from bottom
      loadMoreChannels()
    }
  })

  // Initial load if page is not full
  document.addEventListener('DOMContentLoaded', () => {
    if (
      document.documentElement.scrollHeight <=
      document.documentElement.clientHeight
    ) {
      loadMoreChannels()
    }
  })
</script>
{% endblock %}
