{% extends 'base.html' %} {% block title %}TV Channel Importer{% endblock %} {%
block content %}
<div class="container mt-4">
  <h2>TV Channel Importer</h2>
  <p>Paste your TV channel list below (tvg-name and URL format).</p>

  <div class="mb-3">
    <textarea
      class="form-control"
      id="channelListInput"
      rows="15"
      placeholder="tvg-name: CHANNEL NAME&#10;URL: http://example.com/stream.ts"
    ></textarea>
  </div>
  <button class="btn btn-primary" id="processListBtn">
    Process List
    <span
      class="spinner-border spinner-border-sm d-none"
      role="status"
      aria-hidden="true"
      id="processingSpinner"
    ></span>
  </button>
  <button class="btn btn-secondary" id="clearCacheBtn">Clear Cache</button>
  <button
    class="btn btn-info ms-2"
    data-bs-toggle="modal"
    data-bs-target="#sourceConfigModal"
  >
    Configure Source
  </button>
  <span class="ms-3" id="channelCountLabel">Total Channels: 0</span>
  <hr />

  <h3>Processing Results</h3>
  <ul class="nav nav-tabs mb-3" id="resultsTab" role="tablist">
    <li class="nav-item" role="presentation">
      <button
        class="nav-link active"
        id="all-tab"
        data-bs-toggle="tab"
        data-bs-target="#all-channels"
        type="button"
        role="tab"
        aria-controls="all-channels"
        aria-selected="true"
      >
        All (<span id="allCount">0</span>)
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button
        class="nav-link"
        id="exists-tab"
        data-bs-toggle="tab"
        data-bs-target="#exists-channels"
        type="button"
        role="tab"
        aria-controls="exists-channels"
        aria-selected="false"
      >
        Exists in DB (<span id="existsCount">0</span>)
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button
        class="nav-link"
        id="new-tab"
        data-bs-toggle="tab"
        data-bs-target="#new-channels"
        type="button"
        role="tab"
        aria-controls="new-channels"
        aria-selected="false"
      >
        Doesn't Exist in DB (<span id="newCount">0</span>)
      </button>
    </li>
  </ul>
  <div class="tab-content" id="resultsTabContent">
    <div
      class="tab-pane fade show active"
      id="all-channels"
      role="tabpanel"
      aria-labelledby="all-tab"
    >
      <div id="allResultsContainer">
        <!-- All results will be displayed here -->
      </div>
    </div>
    <div
      class="tab-pane fade"
      id="exists-channels"
      role="tabpanel"
      aria-labelledby="exists-tab"
    >
      <div id="existsResultsContainer">
        <!-- Channels existing in DB will be displayed here -->
      </div>
    </div>
    <div
      class="tab-pane fade"
      id="new-channels"
      role="tabpanel"
      aria-labelledby="new-tab"
    >
      <div id="newResultsContainer">
        <!-- New channels will be displayed here -->
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const channelListInput = document.getElementById('channelListInput')
    const processListBtn = document.getElementById('processListBtn')
    const clearCacheBtn = document.getElementById('clearCacheBtn')
    const processingSpinner = document.getElementById('processingSpinner')
    const channelCountLabel = document.getElementById('channelCountLabel')

    const allResultsContainer = document.getElementById('allResultsContainer')
    const existsResultsContainer = document.getElementById(
      'existsResultsContainer'
    )
    const newResultsContainer = document.getElementById('newResultsContainer')

    const allCountSpan = document.getElementById('allCount')
    const existsCountSpan = document.getElementById('existsCount')
    const newCountSpan = document.getElementById('newCount')

    // Config inputs
    const providerIdInput = document.getElementById('providerIdInput')
    const priorityInput = document.getElementById('priorityInput')
    const retryCountInput = document.getElementById('retryCountInput')
    const isActiveInput = document.getElementById('isActiveInput')
    const isExternalInput = document.getElementById('isExternalInput')
    const saveConfigBtn = document.getElementById('saveConfigBtn')

    let processedChannelsCache =
      JSON.parse(localStorage.getItem('processedChannelsCache')) || {}
    let allProcessedChannelsData = [] // Store full channel data with status

    let sourceConfig = JSON.parse(localStorage.getItem('sourceConfig')) || {
      providerId: 0,
      priority: 0,
      retryCount: 0,
      isActive: true,
      isExternal: false,
    }

    // Load config from local storage
    providerIdInput.value = sourceConfig.providerId
    priorityInput.value = sourceConfig.priority
    retryCountInput.value = sourceConfig.retryCount
    isActiveInput.checked = sourceConfig.isActive
    isExternalInput.checked = sourceConfig.isExternal

    function saveCache() {
      localStorage.setItem(
        'processedChannelsCache',
        JSON.stringify(processedChannelsCache)
      )
    }

    function saveSourceConfig() {
      sourceConfig = {
        providerId: parseInt(providerIdInput.value),
        priority: parseInt(priorityInput.value),
        retryCount: parseInt(retryCountInput.value),
        isActive: isActiveInput.checked,
        isExternal: isExternalInput.checked,
      }
      localStorage.setItem('sourceConfig', JSON.stringify(sourceConfig))
      alert('Source configuration saved!')
    }

    saveConfigBtn.addEventListener('click', saveSourceConfig)

    function displayResults(channelsToDisplay, container, filterType) {
      container.innerHTML = ''
      let count = 0
      channelsToDisplay.forEach((channel, index) => {
        // Only display if not marked as done in cache
        if (!processedChannelsCache[channel.tvgName]) {
          const card = document.createElement('div')
          card.className = `card mb-2`
          card.id = `channel-${filterType}-${index}` // Unique ID for each tab
          card.dataset.tvgName = channel.tvgName // Add data attribute for easy lookup

          let statusBadges = ''
          let addSourceMapBtnDisabled = true
          let matchChannelBtn = ''

          if (channel.channelExists) {
            statusBadges +=
              '<span class="badge bg-success me-1">Channel Exists in DB</span>'
            addSourceMapBtnDisabled = false // Enable button if channel exists
          } else {
            statusBadges +=
              '<span class="badge bg-warning me-1">Channel New</span>'

            // Show additional information about JSON matches
            if (channel.hasJsonMatches) {
              if (channel.singleJsonMatch) {
                statusBadges +=
                  '<span class="badge bg-info me-1">Auto-Match Available</span>'
                matchChannelBtn = `<button class="btn btn-sm btn-success import-json-btn" data-tvg-name="${channel.tvgName}" data-original-url="${channel.url}">Auto-Match & Add</button>`
              } else {
                statusBadges +=
                  '<span class="badge bg-info me-1">Multiple Matches Found</span>'
                matchChannelBtn = `<button class="btn btn-sm btn-info import-json-btn" data-tvg-name="${channel.tvgName}" data-original-url="${channel.url}">Choose Match</button>`
              }
            } else {
              statusBadges +=
                '<span class="badge bg-secondary me-1">No Auto-Match</span>'
              matchChannelBtn = `<button class="btn btn-sm btn-warning import-json-btn" data-tvg-name="${channel.tvgName}" data-original-url="${channel.url}">Manual Match</button>`
            }
            addSourceMapBtnDisabled = true // Disable if new
          }

          if (channel.sourceExists) {
            statusBadges += '<span class="badge bg-info">Source Exists</span>'
            addSourceMapBtnDisabled = true // Disable if source already exists
          } else {
            statusBadges +=
              '<span class="badge bg-primary">Source Not Added</span>'
          }

          if (channel.error) {
            statusBadges += `<span class="badge bg-danger">Error: ${channel.error}</span>`
            addSourceMapBtnDisabled = true // Disable on error
          }

          card.innerHTML = `
                        <div class="card-body">
                            <h5 class="card-title">${channel.tvgName}</h5>
                            <p class="card-text">URL: <a href="${
                              channel.url
                            }" target="_blank">${channel.url}</a></p>
                            <div class="status-indicators">
                                ${statusBadges}
                            </div>
                            <button class="btn btn-sm btn-outline-success mark-done-btn" data-tvg-name="${
                              channel.tvgName
                            }">Mark Done</button>
                            <button class="btn btn-sm btn-primary add-source-map-btn" data-tvg-name="${
                              channel.tvgName
                            }" data-original-url="${
            channel.url
          }" data-channel-id="${channel.channelId || ''}" ${
            addSourceMapBtnDisabled ? 'disabled' : ''
          }>Add Source & Map</button>
                            ${
                              !channel.channelExists
                                ? `<button class="btn btn-sm btn-info import-json-btn" data-tvg-name="${channel.tvgName}" data-original-url="${channel.url}">Match Channel</button>`
                                : ''
                            }
                        </div>
                    `
          container.appendChild(card)
          count++
        }
      })
      return count
    }

    function updateTabCounts() {
      const allCount = allProcessedChannelsData.filter(
        channel => !processedChannelsCache[channel.tvgName]
      ).length
      const existsCount = allProcessedChannelsData.filter(
        channel =>
          !processedChannelsCache[channel.tvgName] && channel.channelExists
      ).length
      const newCount = allProcessedChannelsData.filter(
        channel =>
          !processedChannelsCache[channel.tvgName] && !channel.channelExists
      ).length

      allCountSpan.textContent = allCount
      existsCountSpan.textContent = existsCount
      newCountSpan.textContent = newCount
    }

    function filterAndDisplayChannels(filterType) {
      // Clear all containers first to avoid duplication when switching tabs
      allResultsContainer.innerHTML = ''
      existsResultsContainer.innerHTML = ''
      newResultsContainer.innerHTML = ''

      let channelsToDisplay = []
      let targetContainer = null

      if (filterType === 'all') {
        channelsToDisplay = allProcessedChannelsData
        targetContainer = allResultsContainer
      } else if (filterType === 'exists') {
        channelsToDisplay = allProcessedChannelsData.filter(
          channel => channel.channelExists
        )
        targetContainer = existsResultsContainer
      } else if (filterType === 'new') {
        channelsToDisplay = allProcessedChannelsData.filter(
          channel => !channel.channelExists
        )
        targetContainer = newResultsContainer
      }

      if (targetContainer) {
        displayResults(channelsToDisplay, targetContainer, filterType)
      }
      updateTabCounts()
    }

    // Event listeners for tabs
    document
      .getElementById('all-tab')
      .addEventListener('shown.bs.tab', () => filterAndDisplayChannels('all'))
    document
      .getElementById('exists-tab')
      .addEventListener('shown.bs.tab', () =>
        filterAndDisplayChannels('exists')
      )
    document
      .getElementById('new-tab')
      .addEventListener('shown.bs.tab', () => filterAndDisplayChannels('new'))

    // Update channel count on input change
    channelListInput.addEventListener('input', function () {
      const rawList = channelListInput.value
      const lines = rawList.split('\n').filter(line => line.trim() !== '')
      const channelCount = Math.floor(lines.length / 2)
      channelCountLabel.textContent = `Total Channels: ${channelCount}`
    })

    // Function to process channels in parallel batches with real-time updates
    async function processChannelsInBatches(channelsToProcess, batchSize = 5) {
      let processedCount = 0
      const totalChannels = channelsToProcess.length
      const progressBar = document.getElementById('progressBar')
      const progressText = document.getElementById('progressText')

      // Process channels in batches
      for (let i = 0; i < channelsToProcess.length; i += batchSize) {
        const batch = channelsToProcess.slice(i, i + batchSize)

        // Process batch in parallel
        const batchPromises = batch.map(async channel => {
          if (processedChannelsCache[channel.tvgName]) {
            // If already processed and marked done, just add to allProcessedChannelsData
            const processedChannel = { ...channel, processed: true }
            allProcessedChannelsData.push(processedChannel)
            return processedChannel
          }

          try {
            const response = await fetch('/process_channel', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(channel),
            })
            const result = await response.json()
            const processedChannel = { ...channel, ...result }
            allProcessedChannelsData.push(processedChannel)
            return processedChannel
          } catch (error) {
            console.error('Error processing channel:', channel.tvgName, error)
            const errorChannel = {
              ...channel,
              error: 'Network error',
              channelExists: false,
              sourceExists: false,
              channelId: null,
            }
            allProcessedChannelsData.push(errorChannel)
            return errorChannel
          }
        })

        // Wait for batch to complete and update UI immediately
        const batchResults = await Promise.all(batchPromises)

        // Update progress
        processedCount += batchResults.length
        const progressPercent = (processedCount / totalChannels) * 100
        progressBar.style.width = `${progressPercent}%`
        progressText.textContent = `${processedCount} / ${totalChannels}`

        // Update display immediately after each batch
        filterAndDisplayChannels(
          document.querySelector('.nav-link.active').id.replace('-tab', '')
        )

        // Small delay to prevent overwhelming the server
        if (i + batchSize < channelsToProcess.length) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      }
    }

    processListBtn.addEventListener('click', async function () {
      const rawList = channelListInput.value
      const lines = rawList.split('\n').filter(line => line.trim() !== '')

      let channelsToProcess = []
      for (let i = 0; i < lines.length; i += 2) {
        if (
          lines[i].startsWith('tvg-name:') &&
          lines[i + 1] &&
          lines[i + 1].startsWith('URL:')
        ) {
          const tvgName = lines[i].replace('tvg-name:', '').trim()
          const url = lines[i + 1].replace('URL:', '').trim()
          channelsToProcess.push({ tvgName, url })
        }
      }

      // Show processing indicator and disable button
      processingSpinner.classList.remove('d-none')
      processListBtn.disabled = true

      allProcessedChannelsData = [] // Reset for new processing
      allResultsContainer.innerHTML = ''
      existsResultsContainer.innerHTML = ''
      newResultsContainer.innerHTML = ''

      // Add progress indicator
      const progressContainer = document.createElement('div')
      progressContainer.className = 'mb-3'
      progressContainer.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span>Processing channels...</span>
          <span id="progressText">0 / ${channelsToProcess.length}</span>
        </div>
        <div class="progress">
          <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar"></div>
        </div>
      `
      allResultsContainer.appendChild(progressContainer)

      // Process channels in parallel batches with real-time updates
      await processChannelsInBatches(channelsToProcess, 5) // Process 5 channels at a time

      // Remove progress indicator
      progressContainer.remove()

      // Hide processing indicator and re-enable button
      processingSpinner.classList.add('d-none')
      processListBtn.disabled = false
    })

    // Event delegation for buttons within results containers
    document
      .getElementById('resultsTabContent')
      .addEventListener('click', async function (event) {
        if (event.target.classList.contains('mark-done-btn')) {
          const tvgName = event.target.dataset.tvgName
          processedChannelsCache[tvgName] = true
          saveCache()
          // Hide all cards with this tvgName across all tabs
          document
            .querySelectorAll(`[data-tvg-name="${tvgName}"]`)
            .forEach(card => {
              card.style.display = 'none'
            })
          updateTabCounts()
        } else if (event.target.classList.contains('import-json-btn')) {
          const tvgName = event.target.dataset.tvgName
          const originalUrl = event.target.dataset.originalUrl

          // Store current channel info for modal processing
          currentChannelToProcess = { tvgName, originalUrl }

          // Check if this is an auto-match button (single JSON match available)
          const channelData = allProcessedChannelsData.find(
            ch => ch.tvgName === tvgName
          )
          if (
            channelData &&
            channelData.singleJsonMatch &&
            channelData.jsonMatches &&
            channelData.jsonMatches.length === 1
          ) {
            // Auto-process single match without showing modal
            const jsonChannelId = channelData.jsonMatches[0].id

            const payload = {
              tvgName: tvgName,
              jsonChannelId: jsonChannelId,
            }

            const response = await fetch('/map_channel_from_json_importer', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(payload),
            })
            const result = await response.json()

            // Update the UI for this channel
            document
              .querySelectorAll(`[data-tvg-name="${tvgName}"]`)
              .forEach(card => {
                const statusIndicators =
                  card.querySelector('.status-indicators')
                const addSourceMapBtn = card.querySelector(
                  '.add-source-map-btn'
                )
                const importJsonBtn = card.querySelector('.import-json-btn')

                statusIndicators.innerHTML = '' // Clear previous indicators

                if (result.success) {
                  if (result.channelAddedToDb) {
                    statusIndicators.innerHTML +=
                      '<span class="badge bg-success me-1">Channel Auto-Added to DB</span>'
                  } else {
                    statusIndicators.innerHTML +=
                      '<span class="badge bg-success me-1">Channel Exists in DB</span>'
                  }
                  addSourceMapBtn.disabled = false // Enable Add Source & Map button
                  addSourceMapBtn.dataset.channelId = jsonChannelId // Set the channel ID
                  if (importJsonBtn) {
                    importJsonBtn.style.display = 'none' // Hide the Match Channel button
                  }

                  // Update the channel data in allProcessedChannelsData to reflect it now exists in DB
                  const channelIndex = allProcessedChannelsData.findIndex(
                    ch => ch.tvgName === tvgName
                  )
                  if (channelIndex !== -1) {
                    allProcessedChannelsData[channelIndex].channelExists = true
                    allProcessedChannelsData[channelIndex].channelId =
                      jsonChannelId
                  }
                } else {
                  statusIndicators.innerHTML += `<span class="badge bg-danger">Error auto-mapping channel: ${result.error}</span>`
                }
              })

            // Re-filter and display to update button states and counts
            filterAndDisplayChannels(
              document.querySelector('.nav-link.active').id.replace('-tab', '')
            )

            return // Exit early for auto-match
          }

          // Fetch matches from DB
          const dbResponse = await fetch(
            `/search_db_channels_by_tvg_name?tvgName=${encodeURIComponent(
              tvgName
            )}`
          )
          const dbMatches = await dbResponse.json()

          // Fetch matches from JSON
          const jsonResponse = await fetch(
            `/search_json_channels_cleaned?tvgName=${encodeURIComponent(
              tvgName
            )}`
          )
          const jsonMatches = await jsonResponse.json()

          const modalBody = document.getElementById('channelMatchModalBody')
          modalBody.innerHTML = ''
          document.getElementById('modalTvgName').textContent = tvgName // Set modal title

          let hasMatches = false

          if (dbMatches.length > 0) {
            hasMatches = true
            modalBody.innerHTML += `<p>Matches found in Database:</p>`
            dbMatches.forEach(match => {
              modalBody.innerHTML += `
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="channelMatch" id="match-db-${
                                      match.channel_id
                                    }" value="${match.channel_id}">
                                    <label class="form-check-label" for="match-db-${
                                      match.channel_id
                                    }">
                                        ${match.name} (ID: ${
                match.channel_id
              }) ${match.alt_names ? `[Alt: ${match.alt_names}]` : ''}
                                    </label>
                                </div>
                            `
            })
          }

          if (jsonMatches.length > 0) {
            hasMatches = true
            if (dbMatches.length > 0) {
              modalBody.innerHTML += `<hr>`
            }
            modalBody.innerHTML += `<p>Matches found in JSON data:</p>`
            jsonMatches.forEach(match => {
              modalBody.innerHTML += `
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="channelMatch" id="match-json-${
                                      match.id
                                    }" value="${match.id}">
                                    <label class="form-check-label" for="match-json-${
                                      match.id
                                    }">
                                        ${match.name} (ID: ${match.id}) ${
                match.alt_names ? `[Alt: ${match.alt_names}]` : ''
              }
                                    </label>
                                </div>
                            `
            })
          }

          if (!hasMatches) {
            modalBody.innerHTML =
              '<p>No direct matches found in Database or JSON data.</p>'
          }

          modalBody.innerHTML += `<hr><p>Or, manually enter Channel ID:</p>`
          modalBody.innerHTML += `
                        <div class="mb-3">
                            <label for="manualChannelIdInput" class="form-label">Manual Channel ID</label>
                            <input type="text" class="form-control" id="manualChannelIdInput" placeholder="Enter Channel ID">
                        </div>
                    `

          const channelMatchModal = new bootstrap.Modal(
            document.getElementById('channelMatchModal')
          )
          channelMatchModal.show()
        } else if (event.target.classList.contains('add-source-map-btn')) {
          const tvgName = event.target.dataset.tvgName
          const originalUrl = event.target.dataset.originalUrl
          const channelId = event.target.dataset.channelId // Get channel ID from data attribute

          const sourceIdMatch = originalUrl.split('/').pop().replace('.ts', '')
          const sourceId = sourceIdMatch ? sourceIdMatch : null

          if (!sourceId) {
            alert('Could not extract source_id from URL for adding source.')
            return
          }

          // Use saved source config
          const payload = {
            sourceId: sourceId,
            channelId: channelId,
            sourceUrl: originalUrl,
            tvgName: tvgName,
            providerId: sourceConfig.providerId,
            priority: sourceConfig.priority,
            isActive: sourceConfig.isActive,
            isExternal: sourceConfig.isExternal,
            retryCount: sourceConfig.retryCount,
          }

          const response = await fetch('/add_channel_source_from_importer', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
          })
          const result = await response.json()

          // Find all cards for this tvgName and update their status
          document
            .querySelectorAll(`[data-tvg-name="${tvgName}"]`)
            .forEach(card => {
              const statusIndicators = card.querySelector('.status-indicators')
              const addSourceMapBtn = card.querySelector('.add-source-map-btn')
              statusIndicators.innerHTML = '' // Clear previous indicators

              if (result.success) {
                let message = ''
                if (result.sourceAdded && result.mappingAdded) {
                  message = 'Source Added & Mapped'
                } else if (result.sourceAdded && !result.mappingAdded) {
                  message = 'Source Added, Mapping Failed'
                } else if (!result.sourceAdded && result.mappingAdded) {
                  message = 'Source Existed, Mapping Added'
                } else {
                  message = 'Source Existed & Mapped'
                }
                statusIndicators.innerHTML += `<span class="badge bg-success me-1">${message}</span>`
                processedChannelsCache[tvgName] = true
                saveCache()
                addSourceMapBtn.disabled = true // Disable after successful addition
                card.style.display = 'none' // Hide if done
              } else {
                statusIndicators.innerHTML += `<span class="badge bg-danger">Error adding source: ${result.error}</span>`
              }
            })
          updateTabCounts()
        }
      })

    document
      .getElementById('confirmMatchBtn')
      .addEventListener('click', async function () {
        const selectedRadio = document.querySelector(
          'input[name="channelMatch"]:checked'
        )
        const manualChannelIdInput = document.getElementById(
          'manualChannelIdInput'
        )
        let jsonChannelId = null

        if (selectedRadio) {
          jsonChannelId = selectedRadio.value
        } else if (manualChannelIdInput.value.trim() !== '') {
          jsonChannelId = manualChannelIdInput.value.trim()
        }

        if (!jsonChannelId) {
          alert('Please select a channel or manually enter a Channel ID.')
          return
        }

        const { tvgName, originalUrl } = currentChannelToProcess

        const payload = {
          tvgName: tvgName,
          jsonChannelId: jsonChannelId,
        }

        const response = await fetch('/map_channel_from_json_importer', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        })
        const result = await response.json()

        // Find all cards for this tvgName and update their status
        document
          .querySelectorAll(`[data-tvg-name="${tvgName}"]`)
          .forEach(card => {
            const statusIndicators = card.querySelector('.status-indicators')
            const addSourceMapBtn = card.querySelector('.add-source-map-btn')
            const importJsonBtn = card.querySelector('.import-json-btn')

            statusIndicators.innerHTML = '' // Clear previous indicators

            if (result.success) {
              if (result.channelAddedToDb) {
                statusIndicators.innerHTML +=
                  '<span class="badge bg-success me-1">Channel Added to DB</span>'
              } else {
                statusIndicators.innerHTML +=
                  '<span class="badge bg-success me-1">Channel Exists in DB</span>'
              }
              addSourceMapBtn.disabled = false // Enable Add Source & Map button
              addSourceMapBtn.dataset.channelId = jsonChannelId // Set the channel ID
              if (importJsonBtn) {
                importJsonBtn.style.display = 'none' // Hide the Match Channel button
              }

              // Update the channel data in allProcessedChannelsData to reflect it now exists in DB
              const channelIndex = allProcessedChannelsData.findIndex(
                ch => ch.tvgName === tvgName
              )
              if (channelIndex !== -1) {
                allProcessedChannelsData[channelIndex].channelExists = true
                allProcessedChannelsData[channelIndex].channelId = jsonChannelId
              }
            } else {
              statusIndicators.innerHTML += `<span class="badge bg-danger">Error mapping channel: ${result.error}</span>`
            }
          })
        // Re-filter and display to update button states and counts
        filterAndDisplayChannels(
          document.querySelector('.nav-link.active').id.replace('-tab', '')
        )

        const channelMatchModal = bootstrap.Modal.getInstance(
          document.getElementById('channelMatchModal')
        )
        channelMatchModal.hide()
      })

    clearCacheBtn.addEventListener('click', function () {
      localStorage.removeItem('processedChannelsCache')
      processedChannelsCache = {}
      allProcessedChannelsData = []
      allResultsContainer.innerHTML = ''
      existsResultsContainer.innerHTML = ''
      newResultsContainer.innerHTML = ''
      updateTabCounts()
      alert('Cache cleared!')
    })
  })
</script>

<!-- Source Configuration Modal -->
<div
  class="modal fade"
  id="sourceConfigModal"
  tabindex="-1"
  aria-labelledby="sourceConfigModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="sourceConfigModalLabel">
          Source Configuration
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <div class="row mb-3">
          <div class="col-md-4">
            <label for="providerIdInput" class="form-label">Provider ID</label>
            <input
              type="number"
              class="form-control"
              id="providerIdInput"
              value="0"
            />
          </div>
          <div class="col-md-4">
            <label for="priorityInput" class="form-label">Priority</label>
            <input
              type="number"
              class="form-control"
              id="priorityInput"
              value="0"
            />
          </div>
          <div class="col-md-4">
            <label for="retryCountInput" class="form-label">Retry Count</label>
            <input
              type="number"
              class="form-control"
              id="retryCountInput"
              value="0"
            />
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <div class="form-check">
              <input
                class="form-check-input"
                type="checkbox"
                id="isActiveInput"
                checked
              />
              <label class="form-check-label" for="isActiveInput">
                Is Active
              </label>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-check">
              <input
                class="form-check-input"
                type="checkbox"
                id="isExternalInput"
              />
              <label class="form-check-label" for="isExternalInput">
                Is External
              </label>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Close
        </button>
        <button type="button" class="btn btn-primary" id="saveConfigBtn">
          Save Configuration
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Channel Match Modal -->
<div
  class="modal fade"
  id="channelMatchModal"
  tabindex="-1"
  aria-labelledby="channelMatchModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="channelMatchModalLabel">
          Match Channel for <span id="modalTvgName"></span>
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body" id="channelMatchModalBody">
        <!-- Channel matches will be loaded here -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn btn-primary" id="confirmMatchBtn">
          Confirm Selection
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %}
