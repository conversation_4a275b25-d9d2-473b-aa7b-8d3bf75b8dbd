from datetime import datetime
from sqlite3 import IntegrityError
from flask import Flask, jsonify, render_template, request
from flask import Flask, jsonify, render_template, request
from database_manager import db_manager, db, get_channel_count, get_channel_source_count, get_language_count, get_country_count, get_channel_by_name, get_channel_source_by_url, _add_channel_source_to_db, add_channel_name_mapping, search_channels_by_name, Channel, get_channel_name_mapping_by_tvg_name # Import Channel model and new mapping function
from json_importer import json_importer, load_json_data # Import load_json_data
import re # Import regex module

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///db.sqlite'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)

# Register blueprints
app.register_blueprint(db_manager)
app.register_blueprint(json_importer)

def clean_tvg_name(tvg_name):
    """
    Cleans the tvg-name by removing quality indicators and normalizing for better matching.
    Keywords removed: HD, FHD, SD, 4K, HVEC, TNT, UHD, 8K, HEVC, H264, H265
    Also removes common separators and normalizes spacing.
    """
    if not tvg_name:
        return ""

    # Convert to uppercase for consistent processing
    cleaned_name = tvg_name.upper()

    # Remove quality indicators (case-insensitive, with word boundaries)
    quality_keywords = [
        'HD', 'FHD', 'SD', '4K', 'HVEC', 'TNT', 'UHD', '8K',
        'HEVC', 'H264', 'H265', 'H.264', 'H.265', 'FULL HD',
        'ULTRA HD', 'HIGH DEF', 'STANDARD DEF'
    ]

    for keyword in quality_keywords:
        # Remove keyword with word boundaries, handling various separators
        patterns = [
            rf'\b{re.escape(keyword)}\b',  # Standard word boundary
            rf'[\s\-_\.]*{re.escape(keyword)}[\s\-_\.]*',  # With separators
            rf'^{re.escape(keyword)}[\s\-_\.]+',  # At beginning with separators
            rf'[\s\-_\.]+{re.escape(keyword)}$'   # At end with separators
        ]
        for pattern in patterns:
            cleaned_name = re.sub(pattern, ' ', cleaned_name, flags=re.IGNORECASE)

    # Remove common separators and normalize spacing
    cleaned_name = re.sub(r'[\-_\.]+', ' ', cleaned_name)  # Replace separators with spaces
    cleaned_name = re.sub(r'\s+', ' ', cleaned_name)  # Normalize multiple spaces to single space
    cleaned_name = cleaned_name.strip()  # Remove leading/trailing spaces

    # Remove common prefixes/suffixes that might interfere with matching
    prefixes_suffixes = ['TV', 'CHANNEL', 'LIVE', 'STREAM']
    for term in prefixes_suffixes:
        # Remove if it's a standalone word at beginning or end
        cleaned_name = re.sub(rf'^\b{re.escape(term)}\b\s*', '', cleaned_name, flags=re.IGNORECASE)
        cleaned_name = re.sub(rf'\s*\b{re.escape(term)}\b$', '', cleaned_name, flags=re.IGNORECASE)

    return cleaned_name.strip()

@app.route('/tv_channel_importer')
def tv_channel_importer():
    return render_template('tv_channel_importer.html')

@app.route('/process_channel', methods=['POST'])
def process_channel():
    data = request.get_json()
    tvg_name = data.get('tvgName') # This is the original tvg-name
    url = data.get('url')

    if not tvg_name or not url:
        return jsonify({'error': 'Missing tvgName or URL'}), 400

    channel_exists = False
    source_exists = False
    channel_id = None
    error = None

    try:
        # Extract source_id from URL
        source_id_match = url.split('/')[-1].replace('.ts', '')
        source_id = source_id_match if source_id_match else None

        if not source_id:
            return jsonify({'error': 'Could not extract source_id from URL'}), 400

        # 1. Check if channel exists by tvg_name (get_channel_by_name handles cleaning internally)
        channel = get_channel_by_name(tvg_name)
        if channel:
            channel_exists = True
            channel_id = channel.channel_id
            # 2. Check if source URL already exists for this channel
            existing_source = get_channel_source_by_url(url)
            if existing_source:
                source_exists = True
            # 3. Check if mapping already exists for this tvg_name
            existing_mapping = get_channel_name_mapping_by_tvg_name(tvg_name)
            if not existing_mapping:
                # Automatically create mapping if channel exists but mapping doesn't
                success, msg = add_channel_name_mapping(tvg_name, channel_id)
                if not success:
                    error = msg
        else:
            # Channel does not exist in database, try to find in JSON data automatically
            channel_exists = False
            channel_id = None

            # Try to find matches in JSON data automatically
            from json_importer import search_json_channels_by_name
            json_matches = search_json_channels_by_name(tvg_name)

            # If we find exactly one match, we could auto-suggest it
            # For now, we'll leave it to the frontend to handle multiple matches
            # But we'll indicate if matches were found
            if json_matches:
                # Store the first match as a suggestion (frontend can use this)
                channel_id = json_matches[0]['id'] if len(json_matches) == 1 else None

    except Exception as e:
        error = str(e)

    # Prepare response with additional information for better automation
    response_data = {
        'tvgName': tvg_name,
        'url': url,
        'channelExists': channel_exists,
        'sourceExists': source_exists,
        'channelId': channel_id,
        'error': error
    }

    # If channel doesn't exist, include JSON match information
    if not channel_exists:
        try:
            from json_importer import search_json_channels_by_name
            json_matches = search_json_channels_by_name(tvg_name)
            response_data['jsonMatches'] = json_matches
            response_data['hasJsonMatches'] = len(json_matches) > 0
            response_data['singleJsonMatch'] = len(json_matches) == 1
        except Exception as e:
            response_data['jsonMatches'] = []
            response_data['hasJsonMatches'] = False
            response_data['singleJsonMatch'] = False

    return jsonify(response_data)

@app.route('/search_channels_by_name')
def search_channels_by_name_route():
    query = request.args.get('query', '')
    channels = search_channels_by_name(query)
    channels_data = []
    for channel in channels:
        channels_data.append({
            'channel_id': channel.channel_id,
            'name': channel.name,
            'alt_names': channel.alt_names
        })
    return jsonify(channels_data)

@app.route('/search_db_channels_by_tvg_name')
def search_db_channels_by_tvg_name_route():
    tvg_name = request.args.get('tvgName', '')
    channel = get_channel_by_name(tvg_name) # This function already cleans the name
    if channel:
        return jsonify([{
            'channel_id': channel.channel_id,
            'name': channel.name,
            'alt_names': channel.alt_names
        }])
    return jsonify([])

@app.route('/search_json_channels_cleaned')
def search_json_channels_cleaned_route():
    tvg_name = request.args.get('tvgName', '')
    # Use the existing json_importer's search function (it handles cleaning internally now)
    from json_importer import search_json_channels_by_name
    matches = search_json_channels_by_name(tvg_name)
    return jsonify(matches)

@app.route('/add_channel_source_from_importer', methods=['POST'])
def add_channel_source_from_importer():
    data = request.get_json()
    source_id = data.get('sourceId')
    channel_id = data.get('channelId')
    source_url = data.get('sourceUrl')
    tvg_name = data.get('tvgName')
    provider_id = data.get('providerId')
    priority = data.get('priority')
    is_active = data.get('isActive')
    is_external = data.get('isExternal')
    retry_count = data.get('retryCount')

    if not all([channel_id, source_url, tvg_name]):
        return jsonify({'success': False, 'error': 'Missing data for adding source or mapping'}), 400

    try:
        source_added = False
        mapping_added = False

        # 1. Check if source already exists by URL
        existing_source = get_channel_source_by_url(source_url)
        if existing_source:
            source_added = True # Mark as already existing
        else:
            # Add the channel source
            success_source, msg_source = _add_channel_source_to_db(
                channel_id=channel_id,
                source_url=source_url,
                provider_id=provider_id,
                priority=priority,
                is_active=is_active,
                is_external=is_external,
                retry_count=retry_count
            )
            if not success_source:
                return jsonify({'success': False, 'error': f"Failed to add source: {msg_source}"}), 500
            source_added = True

        # 2. Check if mapping already exists for this tvg_name
        existing_mapping = get_channel_name_mapping_by_tvg_name(tvg_name)
        if existing_mapping and existing_mapping.channel_id == channel_id:
            mapping_added = True # Mark as already existing and correct
        else:
            # Add or update the channel name mapping
            success_mapping, msg_mapping = add_channel_name_mapping(tvg_name, channel_id)
            if not success_mapping:
                return jsonify({'success': False, 'error': f"Failed to update mapping: {msg_mapping}"}), 500
            mapping_added = True

        return jsonify({'success': True, 'sourceAdded': source_added, 'mappingAdded': mapping_added})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/map_channel_from_json_importer', methods=['POST'])
def map_channel_from_json_importer():
    data = request.get_json()
    tvg_name = data.get('tvgName')
    json_channel_id = data.get('jsonChannelId')

    if not all([tvg_name, json_channel_id]):
        return jsonify({'success': False, 'error': 'Missing tvgName or jsonChannelId'}), 400

    channel_added_to_db = False
    try:
        # 1. Check if channel already exists in our database
        existing_channel_in_db = Channel.query.get(json_channel_id)

        if not existing_channel_in_db:
            # If not, load from JSON and add it
            all_json_channels, _, _ = load_json_data()
            channel_data = next((c for c in all_json_channels if c['id'] == json_channel_id), None)

            if not channel_data:
                return jsonify({'success': False, 'error': 'Channel data not found in JSON for import'}), 404

            try:
                new_channel = Channel(
                    channel_id=channel_data['id'], # Use channel_id as per Channel model
                    name=channel_data['name'],
                    alt_names=','.join(channel_data.get('alt_names', [])),
                    country_code=channel_data.get('country', ''), # Assuming 'country' in JSON maps to 'country_code'
                    category_id=None, # JSON doesn't have category_id directly, set to None or map if possible
                    logo_url=channel_data.get('logo', ''), # Assuming 'logo' in JSON maps to 'logo_url'
                    is_active=True, # Default to active
                    created_at=datetime.now().isoformat(), # Set creation timestamp
                    language_code=None, # JSON doesn't have language_code directly, set to None or map if possible
                    group_id=None # JSON doesn't have group_id directly, set to None or map if possible
                )
                db.session.add(new_channel)
                db.session.commit()
                channel_added_to_db = True
            except IntegrityError as e:
                db.session.rollback()
                # This can happen if another process added it between check and add
                existing_channel_in_db = Channel.query.get(json_channel_id)
                if not existing_channel_in_db: # If it still doesn't exist, it's a real integrity error
                    return jsonify({'success': False, 'error': f"Integrity error adding channel: {e}"}), 500
                else: # It was added by another process, proceed as if it existed
                    channel_added_to_db = False # It wasn't *this* request that added it
            except Exception as e:
                db.session.rollback()
                return jsonify({'success': False, 'error': f"Error adding channel to database: {e}"}), 500

        # 2. Map tvg-name to channel_id (add_channel_name_mapping handles cleaning internally)
        success_mapping, msg_mapping = add_channel_name_mapping(tvg_name, json_channel_id)
        if not success_mapping:
            return jsonify({'success': False, 'error': f"Failed to update mapping: {msg_mapping}"}), 500

        return jsonify({'success': True, 'channelAddedToDb': channel_added_to_db})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/')
def index():
    # Placeholder for recent activity. This function needs to be implemented
    # to fetch actual recent activities from your database or logs.
    def get_recent_activity():
        # Example: return a dummy list for now
        return []

    recent_activity = get_recent_activity()
    return render_template('index.html', recent_activity=recent_activity)

@app.route('/db')
def db_overview():
    channel_count = get_channel_count()
    channel_source_count = get_channel_source_count()
    language_count = get_language_count()
    country_count = get_country_count()
    return render_template('db_manager/overview.html', channel_count=channel_count, channel_source_count=channel_source_count, language_count=language_count, country_count=country_count)

@app.route('/get_existing_channel_ids')
def get_existing_channel_ids():
    from database_manager import Channel # Import Channel model here to avoid circular dependency if not already imported
    existing_ids = [channel.id for channel in Channel.query.with_entities(Channel.id).all()]
    return jsonify({'existing_ids': existing_ids})

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)
