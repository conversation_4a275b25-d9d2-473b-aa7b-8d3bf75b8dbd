{% extends "base.html" %} {% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
  <h2>Manage Languages</h2>
  <a href="/db/languages/add" class="btn btn-primary">
    <i class="bi bi-plus-circle"></i> Add Language
  </a>
</div>

<form method="GET" class="mb-4">
  <div class="input-group">
    <input
      type="text"
      class="form-control"
      placeholder="Search languages..."
      name="search_query"
      value="{{ search_query if search_query }}"
    />
    <button class="btn btn-outline-secondary" type="submit">Search</button>
  </div>
</form>

<div class="table-responsive">
  <table class="table table-striped table-hover">
    <thead>
      <tr>
        <th>Code</th>
        <th>Name</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      {% for language in languages %}
      <tr>
        <td>{{ language.code }}</td>
        <td>{{ language.name }}</td>
        <td>
          <a
            href="/db/languages/edit/{{ language.code }}"
            class="btn btn-sm btn-warning"
          >
            <i class="bi bi-pencil"></i>
          </a>
          <a
            href="/db/languages/delete/{{ language.code }}"
            class="btn btn-sm btn-danger"
            onclick="return confirm('Are you sure you want to delete this language?')"
          >
            <i class="bi bi-trash"></i>
          </a>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>

<div id="loading-spinner" class="text-center my-3" style="display: none">
  <div class="spinner-border text-primary" role="status">
    <span class="visually-hidden">Loading...</span>
  </div>
</div>

<script>
  let offset = parseInt('{{ offset + limit }}')
  const limit = parseInt('{{ limit }}')
  const search_query = "{{ search_query | default('') }}"
  let isLoading = false
  let allLanguagesLoaded = false

  function loadMoreLanguages() {
    if (isLoading || allLanguagesLoaded) {
      return
    }
    isLoading = true
    document.getElementById('loading-spinner').style.display = 'block'

    const url = `/db/languages/load_more?offset=${offset}&limit=${limit}&search_query=${search_query}`

    fetch(url)
      .then(response => response.json())
      .then(data => {
        if (data.length === 0) {
          allLanguagesLoaded = true
          console.log('All languages loaded.')
        } else {
          const tbody = document.querySelector('table tbody')
          data.forEach(language => {
            const row = `
              <tr>
                <td>${language.code}</td>
                <td>${language.name}</td>
                <td>
                  <a href="/db/languages/edit/${language.code}" class="btn btn-sm btn-warning">
                    <i class="bi bi-pencil"></i>
                  </a>
                  <a href="/db/languages/delete/${language.code}" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this language?')">
                    <i class="bi bi-trash"></i>
                  </a>
                </td>
              </tr>
            `
            tbody.insertAdjacentHTML('beforeend', row)
          })
          offset += data.length
        }
      })
      .catch(error => {
        console.error('Error loading more languages:', error)
      })
      .finally(() => {
        isLoading = false
        document.getElementById('loading-spinner').style.display = 'none'
      })
  }

  window.addEventListener('scroll', () => {
    const { scrollTop, scrollHeight, clientHeight } = document.documentElement
    if (scrollTop + clientHeight >= scrollHeight - 500) {
      // 500px from bottom
      loadMoreLanguages()
    }
  })

  // Initial load if page is not full
  document.addEventListener('DOMContentLoaded', () => {
    if (
      document.documentElement.scrollHeight <=
      document.documentElement.clientHeight
    ) {
      loadMoreLanguages()
    }
  })
</script>
{% endblock %}
