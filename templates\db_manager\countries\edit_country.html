{% extends "base.html" %} {% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
  <h2>Edit Country</h2>
</div>

<form method="POST">
  <div class="mb-3">
    <label for="country_code" class="form-label">Country Code</label>
    <input
      type="text"
      class="form-control"
      id="country_code"
      name="country_code"
      value="{{ country.country_code }}"
      readonly
    />
  </div>
  <div class="mb-3">
    <label for="name" class="form-label">Country Name</label>
    <input
      type="text"
      class="form-control"
      id="name"
      name="name"
      value="{{ country.name }}"
      required
    />
  </div>
  <div class="mb-3">
    <label for="flag" class="form-label">Flag URL</label>
    <input
      type="url"
      class="form-control"
      id="flag"
      name="flag"
      value="{{ country.flag if country.flag }}"
    />
  </div>
  <button type="submit" class="btn btn-primary">Update Country</button>
  <a href="/db/countries" class="btn btn-secondary">Cancel</a>
</form>
{% endblock %}
