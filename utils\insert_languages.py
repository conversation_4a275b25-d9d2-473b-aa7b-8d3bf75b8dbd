import sqlite3
import os
import json

def insert_missing_languages(db_path, json_file_path):
    languages_to_insert = []
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            languages_to_insert = json.load(f)
    except FileNotFoundError:
        print(f"Error: JSON file not found at '{json_file_path}'")
        return
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from '{json_file_path}'")
        return

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        for lang in languages_to_insert:
            code = lang.get("code")
            name = lang.get("name")

            if not code or not name:
                print(f"Skipping invalid language entry: {lang}")
                continue

            # Check if language already exists
            cursor.execute("SELECT COUNT(*) FROM languages WHERE code = ?", (code,))
            if cursor.fetchone()[0] == 0:
                cursor.execute("INSERT INTO languages (code, name) VALUES (?, ?)", (code, name))
                print(f"Inserted language: {name} ({code})")
            else:
                print(f"Language already exists: {name} ({code}) - Skipping")

        conn.commit()
        print("Language insertion process completed.")

    except sqlite3.Error as e:
        print(f"Database error: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    current_dir = os.path.dirname(__file__)
    project_root = os.path.abspath(os.path.join(current_dir, os.pardir))

    db_file = "db.sqlite"
    db_path = os.path.join(project_root, db_file)

    json_file = "data/languages.json"
    json_file_path = os.path.join(project_root, json_file)

    # Fallback for common alternative location if not found in project root
    if not os.path.exists(db_path):
        db_path = os.path.join(project_root, "instance", db_file)
        if not os.path.exists(db_path):
            print(f"Error: Database file '{db_file}' not found at '{os.path.join(project_root, db_file)}' or '{os.path.join(project_root, 'instance', db_file)}'")
            exit(1)

    print(f"Attempting to connect to database at: {db_path}")
    print(f"Attempting to read languages from: {json_file_path}")
    insert_missing_languages(db_path, json_file_path)
