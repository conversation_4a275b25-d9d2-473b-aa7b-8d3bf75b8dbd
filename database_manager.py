import re
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy.exc import IntegrityError

db_manager = Blueprint('db_manager', __name__)
db = SQLAlchemy()

class Channel(db.Model):
    __tablename__ = 'channels'
    channel_id = db.Column(db.Text, primary_key=True)
    name = db.Column(db.Text, nullable=False)
    alt_names = db.Column(db.Text)
    country_code = db.Column(db.Text)
    category_id = db.Column(db.Integer)
    logo_url = db.Column(db.Text)
    is_active = db.Column(db.Bo<PERSON>an, default=True)
    created_at = db.Column(db.Text) # Assuming CURRENT_TIMESTAMP is handled by DB
    language_code = db.Column(db.Text)
    group_id = db.Column(db.Integer)

class ChannelSource(db.Model):
    __tablename__ = 'channel_sources'
    source_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    channel_id = db.Column(db.Text, nullable=False)
    source_url = db.Column(db.Text, nullable=False, unique=True) # Keep source_url as unique
    provider_id = db.Column(db.Integer)
    priority = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    last_checked = db.Column(db.Text)
    last_status = db.Column(db.Integer)
    retry_count = db.Column(db.Integer, default=0)
    is_external = db.Column(db.Boolean, default=False)

class ChannelNameMapping(db.Model):
    __tablename__ = 'channel_name_mappings'
    tvg_name = db.Column(db.Text, primary_key=True)
    channel_id = db.Column(db.Text, db.ForeignKey('channels.channel_id'), nullable=False)

class Language(db.Model):
    __tablename__ = 'languages'
    code = db.Column(db.Text, primary_key=True)
    name = db.Column(db.Text, nullable=False)

class Country(db.Model):
    __tablename__ = 'countries'
    code = db.Column(db.Text, primary_key=True)
    name = db.Column(db.Text, nullable=False)
    flag = db.Column(db.Text)

@db_manager.route('/db/languages')
def list_languages():
    search_query = request.args.get('search_query')
    offset = request.args.get('offset', 0, type=int)
    limit = request.args.get('limit', 100, type=int)

    languages_query = Language.query

    if search_query:
        countries_query = countries_query.filter(
            (Country.name.ilike(f'%{search_query}%')) |
            (Country.code.ilike(f'%{search_query}%'))
        )

    languages = languages_query.offset(offset).limit(limit).all()

    return render_template('db_manager/languages/languages.html', languages=languages, search_query=search_query, offset=offset, limit=limit)

@db_manager.route('/db/languages/load_more')
def load_more_languages():
    search_query = request.args.get('search_query')
    offset = request.args.get('offset', 0, type=int)
    limit = request.args.get('limit', 100, type=int)

    languages_query = Language.query

    if search_query:
        languages_query = languages_query.filter(
            (Language.name.ilike(f'%{search_query}%')) |
            (Language.code.ilike(f'%{search_query}%'))
        )

    languages = languages_query.offset(offset).limit(limit).all()

    languages_data = []
    for lang in languages:
        languages_data.append({
            'code': lang.code,
            'name': lang.name
        })
    return jsonify(languages_data)

@db_manager.route('/db/languages/add', methods=['GET', 'POST'])
def add_language():
    if request.method == 'POST':
        try:
            language = Language(
                code=request.form['code'],
                name=request.form['name']
            )
            db.session.add(language)
            db.session.commit()
            flash('Language added successfully!', 'success')
            return redirect(url_for('db_manager.list_languages'))
        except IntegrityError:
            db.session.rollback()
            flash('Language Code already exists!', 'danger')

    return render_template('db_manager/languages/add_language.html')

@db_manager.route('/db/languages/edit/<language_code>', methods=['GET', 'POST'])
def edit_language(language_code):
    language = Language.query.filter_by(code=language_code).first_or_404()
    if request.method == 'POST':
        language.name = request.form['name']
        db.session.commit()
        flash('Language updated successfully!', 'success')
        return redirect(url_for('db_manager.list_languages'))

    return render_template('db_manager/languages/edit_language.html', language=language)

@db_manager.route('/db/languages/delete/<language_code>')
def delete_language(language_code):
    language = Language.query.filter_by(code=language_code).first_or_404()
    db.session.delete(language)
    db.session.commit()
    flash('Language deleted successfully!', 'success')
    return redirect(url_for('db_manager.list_languages'))

@db_manager.route('/db/channels')
def list_channels():
    search_query = request.args.get('search_query')
    offset = request.args.get('offset', 0, type=int)
    limit = request.args.get('limit', 100, type=int)

    channels_query = Channel.query

    if search_query:
        channels_query = channels_query.filter(
            (Channel.name.ilike(f'%{search_query}%')) |
            (Channel.alt_names.ilike(f'%{search_query}%')) |
            (Channel.channel_id.ilike(f'%{search_query}%')) |
            (Channel.country_code.ilike(f'%{search_query}%')) |
            (Channel.language_code.ilike(f'%{search_query}%'))
        )

    channels = channels_query.offset(offset).limit(limit).all()

    return render_template('db_manager/channels/channels.html', channels=channels, search_query=search_query, offset=offset, limit=limit)

@db_manager.route('/db/channels/load_more')
def load_more_channels():
    search_query = request.args.get('search_query')
    offset = request.args.get('offset', 0, type=int)
    limit = request.args.get('limit', 100, type=int)

    channels_query = Channel.query

    if search_query:
        channels_query = channels_query.filter(
            (Channel.name.ilike(f'%{search_query}%')) |
            (Channel.alt_names.ilike(f'%{search_query}%')) |
            (Channel.channel_id.ilike(f'%{search_query}%')) |
            (Channel.country_code.ilike(f'%{search_query}%')) |
            (Channel.language_code.ilike(f'%{search_query}%'))
        )

    channels = channels_query.offset(offset).limit(limit).all()

    channels_data = []
    for channel in channels:
        channels_data.append({
            'channel_id': channel.channel_id,
            'name': channel.name,
            'alt_names': channel.alt_names,
            'country_code': channel.country_code,
            'category_id': channel.category_id,
            'logo_url': channel.logo_url,
            'is_active': channel.is_active,
            'created_at': channel.created_at,
            'language_code': channel.language_code,
            'group_id': channel.group_id
        })
    return jsonify(channels_data)

@db_manager.route('/db/channels/add', methods=['GET', 'POST'])
def add_channel():
    if request.method == 'POST':
        try:
            channel = Channel(
                channel_id=request.form['channel_id'],
                name=request.form['name'],
                alt_names=request.form.get('alt_names', ''),
                country_code=request.form.get('country_code', ''),
                category_id=request.form.get('category_id', type=int),
                logo_url=request.form.get('logo_url', ''),
                is_active='is_active' in request.form,
                language_code=request.form.get('language_code', ''),
                group_id=request.form.get('group_id', type=int)
            )
            db.session.add(channel)
            db.session.commit()
            flash('Channel added successfully!', 'success')
            return redirect(url_for('db_manager.list_channels'))
        except IntegrityError:
            db.session.rollback()
            flash('Channel ID already exists!', 'danger')

    return render_template('db_manager/add_channel.html')

@db_manager.route('/db/channels/edit/<channel_id>', methods=['GET', 'POST'])
def edit_channel(channel_id):
    channel = Channel.query.filter_by(channel_id=channel_id).first_or_404()
    if request.method == 'POST':
        channel.name = request.form['name']
        channel.alt_names = request.form.get('alt_names', '')
        channel.country_code = request.form.get('country_code', '')
        channel.category_id = request.form.get('category_id', type=int)
        channel.logo_url = request.form.get('logo_url', '')
        channel.is_active = 'is_active' in request.form
        channel.language_code = request.form.get('language_code', '')
        channel.group_id = request.form.get('group_id', type=int)

        db.session.commit()
        flash('Channel updated successfully!', 'success')
        return redirect(url_for('db_manager.list_channels'))

    return render_template('db_manager/channels/edit_channel.html', channel=channel)

@db_manager.route('/db/channels/delete/<channel_id>')
def delete_channel(channel_id):
    channel = Channel.query.filter_by(channel_id=channel_id).first_or_404()
    db.session.delete(channel)
    db.session.commit()
    flash('Channel deleted successfully!', 'success')
    return redirect(url_for('db_manager.list_channels'))

def clean_channel_name_for_matching(name):
    """
    Cleans channel name for matching using the same logic as clean_tvg_name in app.py
    """
    if not name:
        return ""

    # Convert to uppercase for consistent processing
    cleaned_name = name.upper()

    # Remove quality indicators (case-insensitive, with word boundaries)
    quality_keywords = [
        'HD', 'FHD', 'SD', '4K', 'HVEC', 'TNT', 'UHD', '8K',
        'HEVC', 'H264', 'H265', 'H.264', 'H.265', 'FULL HD',
        'ULTRA HD', 'HIGH DEF', 'STANDARD DEF'
    ]

    for keyword in quality_keywords:
        # Remove keyword with word boundaries, handling various separators
        patterns = [
            rf'\b{re.escape(keyword)}\b',  # Standard word boundary
            rf'[\s\-_\.]*{re.escape(keyword)}[\s\-_\.]*',  # With separators
            rf'^{re.escape(keyword)}[\s\-_\.]+',  # At beginning with separators
            rf'[\s\-_\.]+{re.escape(keyword)}$'   # At end with separators
        ]
        for pattern in patterns:
            cleaned_name = re.sub(pattern, ' ', cleaned_name, flags=re.IGNORECASE)

    # Remove common separators and normalize spacing
    cleaned_name = re.sub(r'[\-_\.]+', ' ', cleaned_name)  # Replace separators with spaces
    cleaned_name = re.sub(r'\s+', ' ', cleaned_name)  # Normalize multiple spaces to single space
    cleaned_name = cleaned_name.strip()  # Remove leading/trailing spaces

    # Remove common prefixes/suffixes that might interfere with matching
    prefixes_suffixes = ['TV', 'CHANNEL', 'LIVE', 'STREAM']
    for term in prefixes_suffixes:
        # Remove if it's a standalone word at beginning or end
        cleaned_name = re.sub(rf'^\b{re.escape(term)}\b\s*', '', cleaned_name, flags=re.IGNORECASE)
        cleaned_name = re.sub(rf'\s*\b{re.escape(term)}\b$', '', cleaned_name, flags=re.IGNORECASE)

    return cleaned_name.strip()

def get_channel_by_name(tvg_name):
    """
    Find channel by name using improved cleaning and matching logic
    """
    cleaned_tvg_name = clean_channel_name_for_matching(tvg_name)

    if not cleaned_tvg_name:
        return None

    # Try to find an exact match first (case-insensitive, cleaned names)
    channels = Channel.query.all()
    for channel in channels:
        cleaned_channel_name = clean_channel_name_for_matching(channel.name)
        if cleaned_channel_name == cleaned_tvg_name:
            return channel

    # If not found, try to find in alt_names (case-insensitive, cleaned names)
    for channel in channels:
        if channel.alt_names:
            alt_names_list = [name.strip() for name in channel.alt_names.split(',')]
            for alt_name in alt_names_list:
                cleaned_alt_name = clean_channel_name_for_matching(alt_name)
                if cleaned_alt_name == cleaned_tvg_name:
                    return channel

    # If still not found, try partial matching (but only if the search term is reasonably long)
    if len(cleaned_tvg_name) >= 3:  # Only do partial matching for terms 3+ characters
        for channel in channels:
            cleaned_channel_name = clean_channel_name_for_matching(channel.name)
            # Only match if the search term is contained in the channel name (not vice versa)
            # and the match is substantial (at least 50% of the shorter string)
            if cleaned_tvg_name in cleaned_channel_name:
                shorter_len = min(len(cleaned_tvg_name), len(cleaned_channel_name))
                longer_len = max(len(cleaned_tvg_name), len(cleaned_channel_name))
                if shorter_len / longer_len >= 0.5:  # At least 50% match
                    return channel

    return None

def get_channel_source_by_url(source_url):
    return ChannelSource.query.filter_by(source_url=source_url).first()

def _add_channel_source_to_db(channel_id, source_url, provider_id=None, priority=0, is_active=True, retry_count=0, is_external=False):
    from datetime import datetime # Import datetime here to ensure it's available
    try:
        channel_source = ChannelSource(
            channel_id=channel_id,
            source_url=source_url,
            provider_id=provider_id,
            priority=priority,
            is_active=is_active,
            last_checked=datetime.now().isoformat(), # Automatically set current timestamp
            last_status=200, # Automatically set a default success status
            retry_count=retry_count,
            is_external=is_external
        )
        db.session.add(channel_source)
        db.session.commit()
        return True, None
    except IntegrityError as e:
        db.session.rollback()
        return False, f"Source ID already exists or other integrity error: {e}"
    except Exception as e:
        db.session.rollback()
        return False, f"An error occurred: {e}"

def add_channel_name_mapping(tvg_name, channel_id):
    """
    Add or update channel name mapping using the original tvg_name (not cleaned)
    This preserves the original name for mapping while using cleaned names for lookups
    """
    try:
        # Use the original tvg_name for mapping storage
        mapping = ChannelNameMapping.query.filter_by(tvg_name=tvg_name).first()
        if mapping:
            mapping.channel_id = channel_id
        else:
            mapping = ChannelNameMapping(tvg_name=tvg_name, channel_id=channel_id)
            db.session.add(mapping)
        db.session.commit()
        return True, None
    except IntegrityError as e:
        db.session.rollback()
        return False, f"Integrity error: {e}"
    except Exception as e:
        db.session.rollback()
        return False, f"An error occurred: {e}"

def search_channels_by_name(query):
    search_pattern = f'%{query.lower()}%'
    channels = Channel.query.filter(
        (db.func.lower(Channel.name).like(search_pattern)) |
        (db.func.lower(Channel.alt_names).like(search_pattern))
    ).all()
    return channels

def get_channel_count():
    return Channel.query.count()

def get_channel_source_count():
    return ChannelSource.query.count()

def get_language_count():
    return Language.query.count()

def get_country_count():
    return Country.query.count()

def get_channel_name_mapping_by_tvg_name(tvg_name):
    """
    Get channel name mapping by original tvg_name (exact match)
    """
    return ChannelNameMapping.query.filter_by(tvg_name=tvg_name).first()

@db_manager.route('/db/channel_name_mappings/manual_add', methods=['POST'])
def manual_add_channel_name_mapping():
    tvg_name = request.form['tvg_name']
    channel_id = request.form['channel_id']

    # Optionally, apply the same cleaning logic as in add_channel_name_mapping if needed for consistency
    # cleaned_tvg_name = re.sub(r'\s*HD\s*', '', tvg_name, flags=re.IGNORECASE).strip()
    cleaned_tvg_name = tvg_name # For manual mapping, assume tvg_name is provided as is, or apply cleaning if desired

    success, message = add_channel_name_mapping(cleaned_tvg_name, channel_id)
    if success:
        flash('Channel Name Mapping added/updated successfully!', 'success')
    else:
        flash(f'Error adding/updating Channel Name Mapping: {message}', 'danger')
    return redirect(url_for('db_manager.list_channel_sources')) # Redirect to a relevant page, e.g., channel sources list

@db_manager.route('/db/channel_sources')
def list_channel_sources():
    search_query = request.args.get('search_query')
    offset = request.args.get('offset', 0, type=int)
    limit = request.args.get('limit', 100, type=int)

    channel_sources_query = ChannelSource.query

    if search_query:
        channel_sources_query = channel_sources_query.filter(
            (ChannelSource.source_id.ilike(f'%{search_query}%')) |
            (ChannelSource.channel_id.ilike(f'%{search_query}%')) |
            (ChannelSource.source_url.ilike(f'%{search_query}%'))
        )

    channel_sources = channel_sources_query.offset(offset).limit(limit).all()

    return render_template('db_manager/channel_sources/channel_sources.html', channel_sources=channel_sources, search_query=search_query, offset=offset, limit=limit)

@db_manager.route('/db/channel_sources/load_more')
def load_more_channel_sources():
    search_query = request.args.get('search_query')
    offset = request.args.get('offset', 0, type=int)
    limit = request.args.get('limit', 100, type=int)

    channel_sources_query = ChannelSource.query

    if search_query:
        channel_sources_query = channel_sources_query.filter(
            (ChannelSource.source_id.ilike(f'%{search_query}%')) |
            (ChannelSource.channel_id.ilike(f'%{search_query}%')) |
            (ChannelSource.source_url.ilike(f'%{search_query}%'))
        )

    channel_sources = channel_sources_query.offset(offset).limit(limit).all()

    channel_sources_data = []
    for source in channel_sources:
        channel_sources_data.append({
            'source_id': source.source_id,
            'channel_id': source.channel_id,
            'source_url': source.source_url,
            'provider_id': source.provider_id,
            'priority': source.priority,
            'is_active': source.is_active,
            'last_checked': source.last_checked,
            'last_status': source.last_status,
            'retry_count': source.retry_count,
            'is_external': source.is_external
        })
    return jsonify(channel_sources_data)

@db_manager.route('/db/channel_sources/add', methods=['GET', 'POST'])
def add_channel_source():
    if request.method == 'POST':
        try:
            channel_source = ChannelSource(
                channel_id=request.form['channel_id'],
                source_url=request.form['source_url'],
                provider_id=request.form.get('provider_id', type=int),
                priority=request.form.get('priority', type=int, default=0),
                is_active='is_active' in request.form,
                last_checked=request.form.get('last_checked', ''),
                last_status=request.form.get('last_status', type=int),
                retry_count=request.form.get('retry_count', type=int, default=0),
                is_external='is_external' in request.form
            )
            db.session.add(channel_source)
            db.session.commit()
            flash('Channel Source added successfully!', 'success')
            return redirect(url_for('db_manager.list_channel_sources'))
        except IntegrityError:
            db.session.rollback()
            flash('Channel Source ID already exists!', 'danger')

    return render_template('db_manager/channel_sources/add_channel_source.html')

@db_manager.route('/db/channel_sources/edit/<source_id>', methods=['GET', 'POST'])
def edit_channel_source(source_id):
    channel_source = ChannelSource.query.filter_by(source_id=source_id).first_or_404()
    if request.method == 'POST':
        channel_source.channel_id = request.form['channel_id']
        channel_source.source_url = request.form['source_url']
        channel_source.provider_id = request.form.get('provider_id', type=int)
        channel_source.priority = request.form.get('priority', type=int, default=0)
        channel_source.is_active = 'is_active' in request.form
        channel_source.last_checked = request.form.get('last_checked', '')
        channel_source.last_status = request.form.get('last_status', type=int)
        channel_source.retry_count = request.form.get('retry_count', type=int, default=0)
        channel_source.is_external = 'is_external' in request.form

        db.session.commit()
        flash('Channel Source updated successfully!', 'success')
        return redirect(url_for('db_manager.list_channel_sources'))

    return render_template('db_manager/channel_sources/edit_channel_source.html', channel_source=channel_source)

@db_manager.route('/db/channel_sources/delete/<source_id>')
def delete_channel_source(source_id):
    channel_source = ChannelSource.query.filter_by(source_id=source_id).first_or_404()
    db.session.delete(channel_source)
    db.session.commit()
    flash('Channel Source deleted successfully!', 'success')
    return redirect(url_for('db_manager.list_channel_sources'))

@db_manager.route('/db/countries')
def list_countries():
    search_query = request.args.get('search_query')
    offset = request.args.get('offset', 0, type=int)
    limit = request.args.get('limit', 100, type=int)

    countries_query = Country.query

    if search_query:
        countries_query = countries_query.filter(
            (Country.name.ilike(f'%{search_query}%')) |
            (Country.code.ilike(f'%{search_query}%'))
        )

    countries = countries_query.offset(offset).limit(limit).all()

    return render_template('db_manager/countries/countries.html', countries=countries, search_query=search_query, offset=offset, limit=limit)

@db_manager.route('/db/countries/load_more')
def load_more_countries():
    search_query = request.args.get('search_query')
    offset = request.args.get('offset', 0, type=int)
    limit = request.args.get('limit', 100, type=int)

    countries_query = Country.query

    if search_query:
        countries_query = countries_query.filter(
            (Country.name.ilike(f'%{search_query}%')) |
            (Country.code.ilike(f'%{search_query}%'))
        )

    countries = countries_query.offset(offset).limit(limit).all()

    countries_data = []
    for country in countries:
        countries_data.append({
            'code': country.code,
            'name': country.name,
            'flag': country.flag
        })
    return jsonify(countries_data)

@db_manager.route('/db/countries/add', methods=['GET', 'POST'])
def add_country():
    if request.method == 'POST':
        try:
            country = Country(
                code=request.form['code'],
                name=request.form['name'],
                flag=request.form.get('flag', '')
            )
            db.session.add(country)
            db.session.commit()
            flash('Country added successfully!', 'success')
            return redirect(url_for('db_manager.list_countries'))
        except IntegrityError:
            db.session.rollback()
            flash('Country Code already exists!', 'danger')

    return render_template('db_manager/countries/add_country.html')

@db_manager.route('/db/countries/edit/<code>', methods=['GET', 'POST'])
def edit_country(code):
    country = Country.query.filter_by(code=code).first_or_404()
    if request.method == 'POST':
        country.name = request.form['name']
        country.flag = request.form.get('flag', '')
        db.session.commit()
        flash('Country updated successfully!', 'success')
        return redirect(url_for('db_manager.list_countries'))

    return render_template('db_manager/countries/edit_country.html', country=country)

@db_manager.route('/db/countries/delete/<code>')
def delete_country(code):
    country = Country.query.filter_by(code=code).first_or_404()
    db.session.delete(country)
    db.session.commit()
    flash('Country deleted successfully!', 'success')
    return redirect(url_for('db_manager.list_countries'))
