{% extends "base.html" %} {% block content %}
<h2>{% if channel %}Edit{% else %}Add{% endif %} Channel</h2>

<form method="POST">
  <div class="row mb-3">
    <div class="col-md-6">
      <label for="channel_id" class="form-label">Channel ID</label>
      <input
        type="text"
        class="form-control"
        id="channel_id"
        name="channel_id"
        value="{{ channel.channel_id if channel }}"
        required
        {%
        if
        channel
        %}readonly{%
        endif
        %}
      />
    </div>
    <div class="col-md-6">
      <label for="name" class="form-label">Channel Name</label>
      <input
        type="text"
        class="form-control"
        id="name"
        name="name"
        value="{{ channel.name if channel }}"
        required
      />
    </div>
  </div>

  <div class="mb-3">
    <label for="alt_names" class="form-label">Alternative Names</label>
    <input
      type="text"
      class="form-control"
      id="alt_names"
      name="alt_names"
      value="{{ channel.alt_names if channel }}"
    />
  </div>

  <div class="mb-3">
    <label for="country_code" class="form-label">Country Code</label>
    <input
      type="text"
      class="form-control"
      id="country_code"
      name="country_code"
      value="{{ channel.country_code if channel }}"
    />
  </div>

  <div class="mb-3">
    <label for="category_id" class="form-label">Category ID</label>
    <input
      type="number"
      class="form-control"
      id="category_id"
      name="category_id"
      value="{{ channel.category_id if channel }}"
    />
  </div>

  <div class="mb-3">
    <label for="logo_url" class="form-label">Logo URL</label>
    <input
      type="text"
      class="form-control"
      id="logo_url"
      name="logo_url"
      value="{{ channel.logo_url if channel }}"
    />
  </div>

  <div class="mb-3 form-check">
    <input
      type="checkbox"
      class="form-check-input"
      id="is_active"
      name="is_active"
      {%
      if
      channel
      and
      channel.is_active
      %}checked{%
      endif
      %}
    />
    <label class="form-check-label" for="is_active">Is Active</label>
  </div>

  <div class="mb-3">
    <label for="language_code" class="form-label">Language Code</label>
    <input
      type="text"
      class="form-control"
      id="language_code"
      name="language_code"
      value="{{ channel.language_code if channel }}"
    />
  </div>

  <div class="mb-3">
    <label for="group_id" class="form-label">Group ID</label>
    <input
      type="number"
      class="form-control"
      id="group_id"
      name="group_id"
      value="{{ channel.group_id if channel }}"
    />
  </div>

  <button type="submit" class="btn btn-primary">
    {% if channel %}Update{% else %}Add{% endif %} Channel
  </button>
</form>
{% endblock %}
